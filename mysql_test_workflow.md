# MCP MySQL 测试工作流程

## 测试环境准备

### 1. 启动 MySQL 服务器
```bash
# 使用 Docker 启动 MySQL (推荐用于测试)
docker run --name test-mysql \
  -e MYSQL_ROOT_PASSWORD=testpassword \
  -e MYSQL_DATABASE=testdb \
  -p 3306:3306 \
  -d mysql:8.0

# 或者启动本地 MySQL 服务
sudo systemctl start mysql  # Linux
brew services start mysql   # macOS
```

### 2. 验证连接
```bash
# 使用命令行客户端测试连接
mysql -h localhost -u root -p testdb
```

## MCP MySQL 工具测试步骤

### 步骤 1: 连接数据库
使用 `connect_db_mysql` 工具连接到数据库：
```
参数:
- host: localhost
- user: root  
- password: testpassword
- database: testdb
```

### 步骤 2: 查看数据库结构
使用 `list_tables_mysql` 查看所有表：
- 应该返回空列表（新数据库）

### 步骤 3: 创建测试表
使用 `execute_mysql` 创建测试表：
```sql
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE,
    age INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
```

### 步骤 4: 查看表结构
使用 `describe_table_mysql` 查看 users 表结构：
```
table: users
```

### 步骤 5: 插入测试数据
使用 `execute_mysql` 插入数据：
```sql
INSERT INTO users (name, email, age) VALUES 
('张三', '<EMAIL>', 25),
('李四', '<EMAIL>', 30),
('王五', '<EMAIL>', 28)
```

### 步骤 6: 查询数据
使用 `query_mysql` 执行各种查询：

1. 查询所有用户：
```sql
SELECT * FROM users
```

2. 条件查询：
```sql
SELECT name, email FROM users WHERE age > 26
```

3. 统计查询：
```sql
SELECT COUNT(*) as total_users FROM users
```

### 步骤 7: 参数化查询测试
使用参数化查询：
```sql
SQL: SELECT * FROM users WHERE age > ? AND name LIKE ?
参数: [25, '%三%']
```

### 步骤 8: 更新数据
使用 `execute_mysql` 更新数据：
```sql
UPDATE users SET age = 26 WHERE name = '张三'
```

### 步骤 9: 删除数据
使用 `execute_mysql` 删除数据：
```sql
DELETE FROM users WHERE age < 26
```

### 步骤 10: 验证更改
再次查询验证更改：
```sql
SELECT * FROM users
```

## 预期结果

1. **连接成功**: 应该能够成功连接到数据库
2. **表操作**: 能够创建、查看表结构
3. **数据操作**: 能够插入、查询、更新、删除数据
4. **参数化查询**: 支持安全的参数化查询
5. **错误处理**: 对于无效操作应该返回适当的错误信息

## 常见问题排查

### 连接失败
- 检查 MySQL 服务是否运行
- 验证主机名、端口、用户名、密码
- 检查防火墙设置

### 权限错误
- 确保用户有足够的数据库权限
- 检查数据库是否存在

### 查询错误
- 验证 SQL 语法
- 检查表名和字段名是否正确
- 确保参数类型匹配

## 清理测试环境

测试完成后清理：
```bash
# 停止并删除 Docker 容器
docker stop test-mysql
docker rm test-mysql

# 或停止本地 MySQL 服务
sudo systemctl stop mysql  # Linux
brew services stop mysql   # macOS
```
